import React, { useEffect, useRef, useState } from 'react';

interface Simple3DViewerProps {
  className?: string;
}

const Simple3DViewer: React.FC<Simple3DViewerProps> = ({ className = '' }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;

    const loadThreeJS = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Check if model exists first
        const modelResponse = await fetch('/models/chibi-character.glb', { method: 'HEAD' });
        if (!modelResponse.ok) {
          throw new Error('Model file not found');
        }

        // Dynamically import Three.js modules
        const THREE = await import('three');
        const { GLTFLoader } = await import('three/examples/jsm/loaders/GLTFLoader.js');
        const { OrbitControls } = await import('three/examples/jsm/controls/OrbitControls.js');

        if (!mounted || !canvasRef.current) return;

        const canvas = canvasRef.current;
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ 
          canvas, 
          antialias: true, 
          alpha: true 
        });

        renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        renderer.outputColorSpace = THREE.SRGBColorSpace;

        // Lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(5, 5, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;
        scene.add(directionalLight);

        // Controls
        const controls = new OrbitControls(camera, canvas);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.enableZoom = true;
        controls.enablePan = false;
        controls.maxPolarAngle = Math.PI / 2;
        controls.minDistance = 2;
        controls.maxDistance = 10;

        // Load model
        const loader = new GLTFLoader();
        loader.load(
          '/models/chibi-character.glb',
          (gltf) => {
            if (!mounted) return;

            const model = gltf.scene;
            
            // Center and scale the model
            const box = new THREE.Box3().setFromObject(model);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());
            
            const maxDim = Math.max(size.x, size.y, size.z);
            const scale = 2 / maxDim;
            model.scale.setScalar(scale);
            
            model.position.sub(center.multiplyScalar(scale));
            model.position.y = -1;

            // Enable shadows
            model.traverse((child) => {
              if (child instanceof THREE.Mesh) {
                child.castShadow = true;
                child.receiveShadow = true;
              }
            });

            scene.add(model);
            setModelLoaded(true);
            setIsLoading(false);

            // Position camera
            camera.position.set(0, 0, 4);
            controls.update();
          },
          (progress) => {
            console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
          },
          (error) => {
            console.error('Error loading model:', error);
            if (mounted) {
              setError('Failed to load 3D model');
              setIsLoading(false);
            }
          }
        );

        // Animation loop
        const animate = () => {
          if (!mounted) return;
          
          requestAnimationFrame(animate);
          controls.update();
          renderer.render(scene, camera);
        };
        animate();

        // Handle resize
        const handleResize = () => {
          if (!mounted || !canvasRef.current) return;
          
          const canvas = canvasRef.current;
          camera.aspect = canvas.clientWidth / canvas.clientHeight;
          camera.updateProjectionMatrix();
          renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        };

        window.addEventListener('resize', handleResize);

        // Cleanup
        return () => {
          window.removeEventListener('resize', handleResize);
          renderer.dispose();
          controls.dispose();
        };

      } catch (err) {
        console.error('Error initializing 3D viewer:', err);
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Failed to load 3D viewer');
          setIsLoading(false);
        }
      }
    };

    loadThreeJS();

    return () => {
      mounted = false;
    };
  }, []);

  if (error) {
    return (
      <div className={`w-full h-full flex flex-col items-center justify-center text-center p-6 ${className}`}>
        <div className="relative mb-6">
          <div className="w-32 h-32 bg-gradient-to-br from-red-400/30 via-orange-400/20 to-yellow-400/30 rounded-full flex items-center justify-center shadow-2xl">
            <span className="text-4xl">⚠️</span>
          </div>
        </div>
        <h3 className="text-lg font-semibold text-card-foreground mb-2">
          3D Model Error
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          {error}
        </p>
        <div className="p-3 bg-accent/50 rounded-lg border border-border">
          <p className="text-xs text-muted-foreground">
            Make sure <code className="bg-background px-1 rounded">chibi-character.glb</code> is in <code className="bg-background px-1 rounded">/public/models/</code>
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`w-full h-full flex flex-col items-center justify-center ${className}`}>
        <div className="relative mb-4">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 border-t-primary"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-2xl">🎮</span>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">Loading 3D Model...</p>
      </div>
    );
  }

  return (
    <div className={`w-full h-full relative ${className}`}>
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ display: 'block' }}
      />
      
      {modelLoaded && (
        <div className="absolute bottom-4 left-4 bg-black/50 text-white text-xs px-2 py-1 rounded">
          🎮 Drag to rotate • Scroll to zoom
        </div>
      )}
    </div>
  );
};

export default Simple3DViewer;
