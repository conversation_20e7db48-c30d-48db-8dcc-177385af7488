import React, { Suspense, useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment, useGLTF } from '@react-three/drei';
import * as THREE from 'three';

// 3D Model Component
interface ChibiCharacterProps {
  modelPath: string;
  scale?: number;
  position?: [number, number, number];
}

const ChibiCharacter: React.FC<ChibiCharacterProps> = ({ modelPath, scale = 1, position = [0, 0, 0] }) => {
  const meshRef = useRef<THREE.Group>(null);
  const [hovered, setHovered] = useState(false);

  // Load the GLTF model
  const gltf = useGLTF(modelPath);
  
  // Animation loop for idle movements
  useFrame((state) => {
    if (meshRef.current) {
      // Gentle floating animation
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.1;
      
      // Subtle rotation when hovered
      if (hovered) {
        meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 2) * 0.1;
      }
    }
  });

  // Handle model loading and setup
  useEffect(() => {
    if (gltf && meshRef.current) {
      // Ensure proper scaling and positioning
      meshRef.current.scale.set(scale, scale, scale);
      
      // Enable shadows
      gltf.scene.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.castShadow = true;
          child.receiveShadow = true;
        }
      });
    }
  }, [gltf, scale]);

  return (
    <group
      ref={meshRef}
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      <primitive object={gltf.scene} />
    </group>
  );
};

// Loading fallback component
const ModelLoader = () => (
  <mesh>
    <boxGeometry args={[1, 2, 1]} />
    <meshStandardMaterial color="#8B5CF6" opacity={0.6} transparent />
  </mesh>
);

// Error fallback component
const ModelError = () => (
  <mesh>
    <sphereGeometry args={[0.8]} />
    <meshStandardMaterial color="#EF4444" />
  </mesh>
);

// Main 3D Model Viewer Component
interface ChibiModel3DProps {
  modelPath?: string;
  className?: string;
  autoRotate?: boolean;
  enableControls?: boolean;
  scale?: number;
  position?: [number, number, number];
  cameraPosition?: [number, number, number];
}

const ChibiModel3D: React.FC<ChibiModel3DProps> = ({
  modelPath = '/models/chibi-character.glb', // Default path
  className = '',
  autoRotate = false,
  enableControls = true,
  scale = 1,
  position = [0, -1, 0],
  cameraPosition = [0, 0, 5],
}) => {
  const [modelExists, setModelExists] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  // Check if model file exists
  useEffect(() => {
    const checkModel = async () => {
      try {
        const response = await fetch(modelPath, { method: 'HEAD' });
        setModelExists(response.ok);
      } catch {
        setModelExists(false);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkModel();
  }, [modelPath]);

  // Fallback content when model doesn't exist
  const FallbackContent = () => (
    <div className="flex flex-col items-center justify-center h-full text-center p-8">
      <div className="w-32 h-32 bg-gradient-to-br from-primary/20 to-purple-400/20 rounded-full mb-4 flex items-center justify-center">
        <span className="text-4xl">🎭</span>
      </div>
      <h3 className="text-lg font-semibold mb-2">3D Character Coming Soon!</h3>
      <p className="text-sm text-muted-foreground max-w-xs">
        Upload your chibi model as <code>chibi-character.glb</code> in the <code>/public/models/</code> folder
      </p>
    </div>
  );

  if (isLoading) {
    return (
      <div className={`w-full h-full flex items-center justify-center ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!modelExists) {
    return (
      <div className={`w-full h-full ${className}`}>
        <FallbackContent />
      </div>
    );
  }

  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        shadows
        camera={{ position: cameraPosition, fov: 50 }}
        gl={{ 
          antialias: true, 
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        {/* Lighting Setup */}
        <ambientLight intensity={0.6} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <pointLight position={[-10, -10, -10]} intensity={0.3} />

        {/* Environment for reflections */}
        <Environment preset="studio" />

        {/* 3D Model */}
        <Suspense fallback={<ModelLoader />}>
          <ChibiCharacter
            modelPath={modelPath}
            scale={scale}
            position={position}
          />
        </Suspense>

        {/* Ground plane for shadows */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]} receiveShadow>
          <planeGeometry args={[10, 10]} />
          <shadowMaterial opacity={0.2} />
        </mesh>

        {/* Camera Controls */}
        {enableControls && (
          <OrbitControls
            autoRotate={autoRotate}
            autoRotateSpeed={2}
            enablePan={false}
            enableZoom={true}
            minDistance={3}
            maxDistance={8}
            minPolarAngle={Math.PI / 6}
            maxPolarAngle={Math.PI / 2}
          />
        )}
      </Canvas>
    </div>
  );
};

export default ChibiModel3D;
