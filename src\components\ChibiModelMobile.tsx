import React, { useState, useEffect } from 'react';

// Mobile-optimized 3D model component with fallback
interface ChibiModelMobileProps {
  className?: string;
  showFallback?: boolean;
}

const ChibiModelMobile: React.FC<ChibiModelMobileProps> = ({
  className = '',
  showFallback = true,
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [modelExists, setModelExists] = useState(false);

  useEffect(() => {
    // Check if device is mobile
    setIsMobile(window.innerWidth < 768);
    
    // Check if 3D model exists
    const checkModel = async () => {
      try {
        const response = await fetch('/models/chibi-character.glb', { method: 'HEAD' });
        setModelExists(response.ok);
      } catch {
        setModelExists(false);
      }
    };
    
    checkModel();

    // Handle resize
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fallback content for when model doesn't exist
  const FallbackContent = () => (
    <div className="relative w-full h-full flex flex-col items-center justify-center text-center p-6">
      {/* Animated Avatar Placeholder */}
      <div className="relative mb-6">
        <div className="w-32 h-32 bg-gradient-to-br from-primary/30 via-purple-400/20 to-blue-400/30 rounded-full flex items-center justify-center shadow-2xl animate-pulse">
          <div className="w-24 h-24 bg-gradient-to-br from-primary/40 to-purple-400/40 rounded-full flex items-center justify-center">
            <span className="text-4xl animate-bounce">👨‍💻</span>
          </div>
        </div>
        
        {/* Floating particles */}
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-primary/60 rounded-full animate-ping"></div>
        <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-purple-400/60 rounded-full animate-ping delay-300"></div>
        <div className="absolute top-1/2 -right-4 w-2 h-2 bg-blue-400/60 rounded-full animate-pulse delay-500"></div>
      </div>

      {/* Text Content */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-card-foreground">
          3D Character Coming Soon! 🎭
        </h3>
        <p className="text-sm text-muted-foreground max-w-xs leading-relaxed">
          I'm working on a custom 3D chibi character that will live here and interact with visitors!
        </p>
        
        {/* Instructions for development */}
        <div className="mt-4 p-3 bg-accent/50 rounded-lg border border-border">
          <p className="text-xs text-muted-foreground">
            <strong>Dev Note:</strong> Place your <code className="bg-background px-1 rounded">chibi-character.glb</code> file in <code className="bg-background px-1 rounded">/public/models/</code>
          </p>
        </div>
      </div>

      {/* Interactive elements */}
      <div className="mt-6 flex space-x-2">
        <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-primary rounded-full animate-bounce delay-100"></div>
        <div className="w-2 h-2 bg-primary rounded-full animate-bounce delay-200"></div>
      </div>
    </div>
  );

  // Loading state
  const LoadingContent = () => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="relative">
        <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 border-t-primary"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-2xl">🎮</span>
        </div>
      </div>
    </div>
  );

  // If model exists and not mobile, we'll use the full 3D component
  if (modelExists && !isMobile) {
    // Lazy load the full 3D component only when needed
    const ChibiModel3D = React.lazy(() => import('./ChibiModel3D'));
    
    return (
      <div className={`w-full h-full ${className}`}>
        <React.Suspense fallback={<LoadingContent />}>
          <ChibiModel3D
            enableControls={true}
            autoRotate={false}
            scale={1.2}
            position={[0, -1, 0]}
            cameraPosition={[0, 0, 4]}
          />
        </React.Suspense>
      </div>
    );
  }

  // For mobile or when model doesn't exist, show fallback
  return (
    <div className={`w-full h-full ${className}`}>
      {showFallback ? <FallbackContent /> : <LoadingContent />}
    </div>
  );
};

export default ChibiModelMobile;
