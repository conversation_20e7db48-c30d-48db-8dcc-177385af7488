import React, { useState, useEffect } from 'react';

interface Simple3DPlaceholderProps {
  className?: string;
}

const Simple3DPlaceholder: React.FC<Simple3DPlaceholderProps> = ({
  className = '',
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [animationPhase, setAnimationPhase] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={`w-full h-full flex flex-col items-center justify-center text-center p-6 ${className}`}>
      {/* Animated 3D-style Character */}
      <div 
        className="relative mb-6 transition-transform duration-300 ease-in-out"
        style={{
          transform: isHovered ? 'scale(1.1) rotateY(10deg)' : 'scale(1) rotateY(0deg)',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Main Character Container */}
        <div className="relative w-32 h-32">
          {/* Background Glow */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-purple-400/20 to-blue-400/30 rounded-full blur-lg animate-pulse"></div>
          
          {/* Character Base */}
          <div className="relative w-full h-full bg-gradient-to-br from-primary/40 to-purple-400/40 rounded-full flex items-center justify-center shadow-2xl border-4 border-white/20">
            {/* Character Face */}
            <div className="text-5xl transition-transform duration-300" style={{
              transform: `rotate(${animationPhase * 5}deg)`,
            }}>
              👨‍💻
            </div>
            
            {/* Glasses Overlay */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-3xl opacity-80">🤓</div>
            </div>
          </div>

          {/* Floating Particles */}
          <div 
            className="absolute -top-2 -right-2 w-4 h-4 bg-primary/60 rounded-full animate-ping"
            style={{ animationDelay: '0s' }}
          ></div>
          <div 
            className="absolute -bottom-2 -left-2 w-3 h-3 bg-purple-400/60 rounded-full animate-ping"
            style={{ animationDelay: '0.5s' }}
          ></div>
          <div 
            className="absolute top-1/2 -right-4 w-2 h-2 bg-blue-400/60 rounded-full animate-pulse"
            style={{ animationDelay: '1s' }}
          ></div>
          <div 
            className="absolute bottom-1/4 -left-4 w-2 h-2 bg-green-400/60 rounded-full animate-bounce"
            style={{ animationDelay: '1.5s' }}
          ></div>
        </div>

        {/* 3D Shadow Effect */}
        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-24 h-6 bg-black/20 rounded-full blur-md"></div>
      </div>

      {/* Text Content */}
      <div className="space-y-3 max-w-xs">
        <h3 className="text-lg font-semibold text-card-foreground flex items-center justify-center gap-2">
          3D Character Coming Soon! 
          <span className="animate-bounce">🎭</span>
        </h3>
        
        <p className="text-sm text-muted-foreground leading-relaxed">
          I'm working on a custom 3D chibi character that will live here and interact with visitors!
        </p>
        
        {/* Feature Preview */}
        <div className="mt-4 p-3 bg-accent/30 rounded-lg border border-border/50">
          <p className="text-xs text-muted-foreground mb-2">
            <strong>Coming Features:</strong>
          </p>
          <div className="flex flex-wrap gap-1 text-xs">
            <span className="bg-primary/20 px-2 py-1 rounded">Interactive 3D</span>
            <span className="bg-purple-400/20 px-2 py-1 rounded">Animations</span>
            <span className="bg-blue-400/20 px-2 py-1 rounded">Touch Controls</span>
          </div>
        </div>

        {/* Instructions for development */}
        <div className="mt-4 p-3 bg-accent/50 rounded-lg border border-border">
          <p className="text-xs text-muted-foreground">
            <strong>Dev Note:</strong> Place your <code className="bg-background px-1 rounded text-primary">chibi-character.glb</code> file in <code className="bg-background px-1 rounded text-primary">/public/models/</code>
          </p>
        </div>
      </div>

      {/* Interactive Loading Animation */}
      <div className="mt-6 flex space-x-2">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className="w-2 h-2 bg-primary rounded-full animate-bounce"
            style={{
              animationDelay: `${i * 0.1}s`,
              animationDuration: '1s',
            }}
          ></div>
        ))}
      </div>

      {/* Hover Instruction */}
      <div className="mt-4 text-xs text-muted-foreground/60">
        {isHovered ? "✨ Looking good!" : "👆 Hover me!"}
      </div>
    </div>
  );
};

export default Simple3DPlaceholder;
