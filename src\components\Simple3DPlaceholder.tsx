import React, { useState, useEffect } from 'react';

interface Simple3DPlaceholderProps {
  className?: string;
  preferSpline?: boolean; // Option to prefer Spline over Three.js
}

const Simple3DViewer = React.lazy(() => import('./Simple3DViewer'));
const SplineViewer = React.lazy(() => import('./SplineViewer'));

const Simple3DPlaceholder: React.FC<Simple3DPlaceholderProps> = ({
  className = '',
  preferSpline = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [animationPhase, setAnimationPhase] = useState(0);
  const [modelExists, setModelExists] = useState(false);
  const [splineExists, setSplineExists] = useState(false);
  const [showViewer, setShowViewer] = useState(false);
  const [viewerType, setViewerType] = useState<'threejs' | 'spline'>('threejs');
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 2000);

    // Check if models exist
    const checkModels = async () => {
      try {
        // Check for GLB model
        const glbResponse = await fetch('/models/chibi-character.glb', { method: 'HEAD' });
        setModelExists(glbResponse.ok);

        // Check for Spline scene
        const splineResponse = await fetch('/models/scene.splinecode', { method: 'HEAD' });
        setSplineExists(splineResponse.ok);

        // Set default viewer type based on preference and availability
        if (preferSpline && splineResponse.ok) {
          setViewerType('spline');
        } else if (glbResponse.ok) {
          setViewerType('threejs');
        } else if (splineResponse.ok) {
          setViewerType('spline');
        }
      } catch {
        setModelExists(false);
        setSplineExists(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkModels();

    return () => clearInterval(interval);
  }, []);

  // Show 3D viewer if requested and model exists
  if (showViewer && (modelExists || splineExists)) {
    const ViewerComponent = viewerType === 'spline' ? SplineViewer : Simple3DViewer;

    return (
      <div className={`w-full h-full relative ${className}`}>
        <React.Suspense fallback={
          <div className="w-full h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 border-t-primary"></div>
          </div>
        }>
          <ViewerComponent className="w-full h-full" />
        </React.Suspense>

        {/* Back button */}
        <button
          onClick={() => setShowViewer(false)}
          className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white px-3 py-1 rounded-full text-sm transition-colors z-10"
        >
          ← Back
        </button>

        {/* Viewer type switcher */}
        {modelExists && splineExists && (
          <div className="absolute top-4 left-4 flex gap-2 z-10">
            <button
              onClick={() => setViewerType('threejs')}
              className={`px-3 py-1 rounded-full text-xs transition-colors ${
                viewerType === 'threejs'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-black/50 text-white hover:bg-black/70'
              }`}
            >
              3D Model
            </button>
            <button
              onClick={() => setViewerType('spline')}
              className={`px-3 py-1 rounded-full text-xs transition-colors ${
                viewerType === 'spline'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-black/50 text-white hover:bg-black/70'
              }`}
            >
              Spline Scene
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`w-full h-full flex flex-col items-center justify-center text-center p-6 relative ${className}`}>
      {/* Animated 3D-style Character */}
      <div 
        className="relative mb-6 transition-transform duration-300 ease-in-out"
        style={{
          transform: isHovered ? 'scale(1.1) rotateY(10deg)' : 'scale(1) rotateY(0deg)',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Main Character Container */}
        <div className="relative w-32 h-32">
          {/* Background Glow */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-purple-400/20 to-blue-400/30 rounded-full blur-lg animate-pulse"></div>
          
          {/* Character Base */}
          <div className="relative w-full h-full bg-gradient-to-br from-primary/40 to-purple-400/40 rounded-full flex items-center justify-center shadow-2xl border-4 border-white/20">
            {/* Character Face */}
            <div className="text-5xl transition-transform duration-300" style={{
              transform: `rotate(${animationPhase * 5}deg)`,
            }}>
              👨‍💻
            </div>
            
            {/* Glasses Overlay */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-3xl opacity-80">🤓</div>
            </div>
          </div>

          {/* Floating Particles */}
          <div 
            className="absolute -top-2 -right-2 w-4 h-4 bg-primary/60 rounded-full animate-ping"
            style={{ animationDelay: '0s' }}
          ></div>
          <div 
            className="absolute -bottom-2 -left-2 w-3 h-3 bg-purple-400/60 rounded-full animate-ping"
            style={{ animationDelay: '0.5s' }}
          ></div>
          <div 
            className="absolute top-1/2 -right-4 w-2 h-2 bg-blue-400/60 rounded-full animate-pulse"
            style={{ animationDelay: '1s' }}
          ></div>
          <div 
            className="absolute bottom-1/4 -left-4 w-2 h-2 bg-green-400/60 rounded-full animate-bounce"
            style={{ animationDelay: '1.5s' }}
          ></div>
        </div>

        {/* 3D Shadow Effect */}
        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-24 h-6 bg-black/20 rounded-full blur-md"></div>
      </div>

      {/* Text Content */}
      <div className="space-y-3 max-w-xs">
        <h3 className="text-lg font-semibold text-card-foreground flex items-center justify-center gap-2">
          {(modelExists || splineExists) ? "3D Scene Ready!" : "3D Scene Coming Soon!"}
          <span className="animate-bounce">{(modelExists || splineExists) ? "🎮" : "🎭"}</span>
        </h3>

        <p className="text-sm text-muted-foreground leading-relaxed">
          {(modelExists || splineExists)
            ? `Interactive 3D ${splineExists ? 'Spline scene' : 'character'} ready to explore!`
            : "I'm working on an interactive 3D scene that will live here!"
          }
        </p>

        {/* Load 3D Scene Button */}
        {(modelExists || splineExists) && !isChecking && (
          <div className="mt-4 space-y-2">
            <button
              onClick={() => {
                setViewerType(preferSpline && splineExists ? 'spline' : modelExists ? 'threejs' : 'spline');
                setShowViewer(true);
              }}
              className="px-6 py-3 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg font-medium transition-colors shadow-lg hover:shadow-xl transform hover:scale-105 duration-200"
            >
              {splineExists && !modelExists ? "🎨 View Spline Scene" :
               modelExists && !splineExists ? "🎮 View 3D Model" :
               "🎮 Explore 3D Scene"}
            </button>

            {/* Show available formats */}
            {modelExists && splineExists && (
              <p className="text-xs text-muted-foreground">
                Both 3D model and Spline scene available
              </p>
            )}
          </div>
        )}
        
        {/* Feature Preview */}
        <div className="mt-4 p-3 bg-accent/30 rounded-lg border border-border/50">
          <p className="text-xs text-muted-foreground mb-2">
            <strong>Features:</strong>
          </p>
          <div className="flex flex-wrap gap-1 text-xs">
            <span className="bg-primary/20 px-2 py-1 rounded">Interactive 3D</span>
            {splineExists && <span className="bg-purple-400/20 px-2 py-1 rounded">Spline Scene</span>}
            {modelExists && <span className="bg-green-400/20 px-2 py-1 rounded">3D Model</span>}
            <span className="bg-blue-400/20 px-2 py-1 rounded">Touch Controls</span>
          </div>
        </div>

        {/* Instructions for development */}
        {!modelExists && !splineExists && (
          <div className="mt-4 p-3 bg-accent/50 rounded-lg border border-border">
            <p className="text-xs text-muted-foreground">
              <strong>Dev Note:</strong><br/>
              • GLB Model: <code className="bg-background px-1 rounded text-primary">chibi-character.glb</code><br/>
              • Spline Scene: <code className="bg-background px-1 rounded text-primary">scene.splinecode</code><br/>
              Place in <code className="bg-background px-1 rounded text-primary">/public/models/</code>
            </p>
          </div>
        )}
      </div>

      {/* Interactive Loading Animation */}
      <div className="mt-6 flex space-x-2">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className="w-2 h-2 bg-primary rounded-full animate-bounce"
            style={{
              animationDelay: `${i * 0.1}s`,
              animationDuration: '1s',
            }}
          ></div>
        ))}
      </div>

      {/* Hover Instruction */}
      <div className="mt-4 text-xs text-muted-foreground/60">
        {isHovered ? "✨ Looking good!" : "👆 Hover me!"}
      </div>
    </div>
  );
};

export default Simple3DPlaceholder;
