import React, { useState, useEffect, Suspense } from 'react';

interface SplineViewerProps {
  className?: string;
  sceneUrl?: string; // URL to the published Spline scene
  splineCodePath?: string; // Path to local .splinecode file
}

// Lazy load Spline component
const Spline = React.lazy(() => import('@splinetool/react-spline'));

const SplineViewer: React.FC<SplineViewerProps> = ({ 
  className = '',
  sceneUrl,
  splineCodePath = '/models/scene.splinecode'
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sceneExists, setSceneExists] = useState(false);
  const [finalSceneUrl, setFinalSceneUrl] = useState<string>('');

  useEffect(() => {
    const checkScene = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // If a direct scene URL is provided, use it
        if (sceneUrl) {
          setFinalSceneUrl(sceneUrl);
          setSceneExists(true);
          setIsLoading(false);
          return;
        }

        // Check if local splinecode file exists
        if (splineCodePath) {
          const response = await fetch(splineCodePath, { method: 'HEAD' });
          if (response.ok) {
            setFinalSceneUrl(splineCodePath);
            setSceneExists(true);
          } else {
            setError('Spline scene file not found');
          }
        } else {
          setError('No scene URL or file path provided');
        }
      } catch (err) {
        console.error('Error checking Spline scene:', err);
        setError('Failed to load Spline scene');
      } finally {
        setIsLoading(false);
      }
    };

    checkScene();
  }, [sceneUrl, splineCodePath]);

  const handleSplineLoad = () => {
    console.log('Spline scene loaded successfully');
    setIsLoading(false);
  };

  const handleSplineError = (error: any) => {
    console.error('Spline scene error:', error);
    setError('Failed to render Spline scene');
    setIsLoading(false);
  };

  if (error) {
    return (
      <div className={`w-full h-full flex flex-col items-center justify-center text-center p-6 ${className}`}>
        <div className="relative mb-6">
          <div className="w-32 h-32 bg-gradient-to-br from-red-400/30 via-orange-400/20 to-yellow-400/30 rounded-full flex items-center justify-center shadow-2xl">
            <span className="text-4xl">🎨</span>
          </div>
        </div>
        <h3 className="text-lg font-semibold text-card-foreground mb-2">
          Spline Scene Error
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          {error}
        </p>
        <div className="p-3 bg-accent/50 rounded-lg border border-border">
          <p className="text-xs text-muted-foreground">
            <strong>Options:</strong><br/>
            • Place <code className="bg-background px-1 rounded">scene.splinecode</code> in <code className="bg-background px-1 rounded">/public/models/</code><br/>
            • Or provide a published Spline scene URL
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`w-full h-full flex flex-col items-center justify-center ${className}`}>
        <div className="relative mb-4">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 border-t-primary"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-2xl">🎨</span>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">Loading Spline Scene...</p>
      </div>
    );
  }

  if (!sceneExists || !finalSceneUrl) {
    return (
      <div className={`w-full h-full flex flex-col items-center justify-center text-center p-6 ${className}`}>
        <div className="relative mb-6">
          <div className="w-32 h-32 bg-gradient-to-br from-purple-400/30 via-pink-400/20 to-blue-400/30 rounded-full flex items-center justify-center shadow-2xl">
            <span className="text-4xl">🎨</span>
          </div>
        </div>
        <h3 className="text-lg font-semibold text-card-foreground mb-2">
          Spline Scene Coming Soon!
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          Interactive 3D scene created with Spline will appear here.
        </p>
        <div className="p-3 bg-accent/50 rounded-lg border border-border">
          <p className="text-xs text-muted-foreground">
            <strong>To add your scene:</strong><br/>
            • Export from Spline as <code className="bg-background px-1 rounded">.splinecode</code><br/>
            • Place in <code className="bg-background px-1 rounded">/public/models/scene.splinecode</code><br/>
            • Or publish online and use the URL
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full h-full relative ${className}`}>
      <Suspense fallback={
        <div className="w-full h-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 border-t-primary"></div>
        </div>
      }>
        <Spline
          scene={finalSceneUrl}
          onLoad={handleSplineLoad}
          onError={handleSplineError}
          style={{ width: '100%', height: '100%' }}
        />
      </Suspense>
      
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary/30 border-t-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading Spline...</p>
          </div>
        </div>
      )}
      
      {/* Controls hint */}
      {!isLoading && sceneExists && (
        <div className="absolute bottom-4 left-4 bg-black/50 text-white text-xs px-2 py-1 rounded">
          🎨 Interactive Spline Scene
        </div>
      )}
    </div>
  );
};

export default SplineViewer;
