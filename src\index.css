
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;

    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;

    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;

    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;

    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;

    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 210 20% 98%;

    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
    --sidebar-background: 224 71.4% 4.1%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 263.4 70% 50.4%;
    --sidebar-primary-foreground: 210 20% 98%;
    --sidebar-accent: 215 27.9% 16.9%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 215 27.9% 16.9%;
    --sidebar-ring: 263.4 70% 50.4%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px; /* For navigation offset */
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    line-height: 1.6;
  }

  .dark body {
    @apply bg-background text-foreground;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display tracking-tight text-foreground;
  }

  .section {
    @apply py-16 md:py-24;
  }

  /* Custom animation classes */
  .hover-scale {
    @apply transition-transform duration-300 ease-in-out hover:scale-105;
  }

  .social-icon {
    @apply transition-all duration-300 hover:text-primary hover:animate-social-bounce;
  }
  
  /* Additional utility classes */
  .glass-card {
    @apply bg-card/80 backdrop-blur-sm rounded-2xl shadow-xl border border-border;
  }
  
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600;
  }
  
  /* Enhanced animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  .animate-float {
    animation: float 4s ease-in-out infinite;
  }
  
  /* Improved scroll snap for sections */
  .scroll-snap-container {
    scroll-snap-type: y mandatory;
    overflow-y: scroll;
    height: 100vh;
  }
  
  .scroll-snap-section {
    scroll-snap-align: start;
    min-height: 100vh;
  }
}
