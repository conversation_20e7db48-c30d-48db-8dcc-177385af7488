/* Image Quality Enhancement Styles */

.image-render-crisp {
  /* Hardware acceleration for smoother rendering */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;

  /* Anti-aliasing improvements */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Prevent blurry scaling */
  -ms-interpolation-mode: bicubic;
}

/* Desktop-only enhancements */
@media (min-width: 768px) {
  .image-render-crisp {
    /* High-quality image rendering for desktop */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -webkit-crisp-edges;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;

    /* Enhanced sharpness and contrast */
    filter: contrast(1.08) saturate(1.03) brightness(1.01);

    /* Smooth transitions */
    transition: filter 0.3s ease;
  }

  .image-render-crisp:hover {
    /* Slight enhancement on hover */
    filter: contrast(1.12) saturate(1.06) brightness(1.02);
  }
}

/* High-DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .image-render-crisp {
    /* Enhanced filtering for high-DPI displays */
    filter: contrast(1.06) saturate(1.02) brightness(1.005);
  }
  
  .image-render-crisp:hover {
    filter: contrast(1.1) saturate(1.04) brightness(1.01);
  }
}

/* Specific enhancements for portfolio images */
.portfolio-image-enhanced {
  /* Basic optimization for all devices */
  transform-origin: center;
}

/* Desktop-only portfolio enhancements */
@media (min-width: 768px) {
  .portfolio-image-enhanced {
    /* Maximum quality settings for desktop */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;

    /* Advanced filtering for professional photos */
    filter:
      contrast(1.1)
      saturate(1.05)
      brightness(1.02);

    /* Smooth scaling */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .portfolio-image-enhanced:hover {
    /* Subtle enhancement on interaction */
    filter:
      contrast(1.15)
      saturate(1.08)
      brightness(1.03);

    transform: scale(1.02);
  }
}

/* Loading state optimization */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Prevent image blur during animations */
.no-blur {
  backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}
