# 3D Models and Spline Scenes

This folder contains 3D assets for the portfolio website.

## Supported Formats

### 1. GLB Models (Three.js)
- **File name:** `chibi-character.glb`
- **Description:** 3D character model in GLB format
- **Features:** Interactive rotation, zoom, professional lighting
- **Tools:** <PERSON><PERSON><PERSON>, Maya, 3ds Max, etc.

### 2. Spline Scenes
- **File name:** `scene.splinecode`
- **Description:** Interactive 3D scene created with Spline
- **Features:** Full interactivity, animations, custom behaviors
- **Tool:** Spline (https://spline.design)

## How to Add Your Spline Scene

### Option 1: Local File
1. Create your scene in Spline (https://spline.design)
2. Export as `.splinecode` file
3. Place the file here as `scene.splinecode`
4. The website will automatically detect and load it

### Option 2: Published Scene URL
1. Publish your scene in Spline
2. Copy the public URL
3. Update the `SplineViewer` component with your URL:
   ```tsx
   <SplineViewer sceneUrl="https://prod.spline.design/your-scene-id/scene.splinecode" />
   ```

## Priority System

The website will automatically choose the best available format:

1. **If `preferSpline={true}`:** Spline scene → GLB model
2. **If `preferSpline={false}`:** GLB model → Spline scene
3. **If both available:** User can switch between them

## File Structure
```
public/models/
├── chibi-character.glb    # 3D character model
├── scene.splinecode       # Spline scene
└── README.md             # This file
```

## Tips for Spline Scenes

- Keep file size under 10MB for good performance
- Test on mobile devices
- Use compressed textures when possible
- Consider loading times for complex scenes
- Add interactive elements for engagement

## Troubleshooting

- **Scene not loading:** Check file name and location
- **Performance issues:** Reduce scene complexity
- **Mobile problems:** Test responsive design
- **CORS errors:** Use published URLs for external scenes
