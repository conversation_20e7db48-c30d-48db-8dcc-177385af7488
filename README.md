# 🌸 Film Portfolio – React + Vite + Tailwind Starter

This is a personal portfolio website built using **React**, **Vite**, and **Tailwind CSS**, with a sleek and modern UI powered by **shadcn/ui**.  
The initial structure and layout were assisted using [Lovable](https://lovable.dev/) to accelerate prototyping and setup.

---

## 🔧 Getting Started

To run this project locally, follow these steps:

### Prerequisites

- Node.js (v18 or later)
- npm (or yarn)

> Tip: You can install Node.js using [nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

### Setup

```bash
# 1. Clone the repository
git clone <YOUR_GIT_URL>
cd <YOUR_PROJECT_NAME>

# 2. Install dependencies
npm install

# 3. Run the development server
npm run dev
```
## 🧰 Tech Stack
- ⚡ **Vite** – Lightning-fast build tool and dev server  
- 🟪 **React** – JavaScript library for building user interfaces  
- 🎨 **Tailwind CSS** – Utility-first CSS framework  
- 🧩 **shadcn/ui** – Accessible component library based on Radix UI  
- ✨ **TypeScript** – Static typing for better developer experience
---
## 🚢 Deployment

You can deploy this project on:

- 🌐 [Vercel](https://vercel.com/)
- ☁️ [Netlify](https://www.netlify.com/)
- 📦 GitHub Pages (via static export)
- 🔧 Any Node.js-compatible hosting

---
## 🌐 Live Demo  
[![View Demo](https://img.shields.io/badge/🚀_Live_Demo-Click_here-blue?style=for-the-badge)](https://film-portfolio-seven.vercel.app/)

🧭 Hosted on **Vercel**

---
## 📄 License

This project is licensed under the MIT License.
You are free to use, modify, and distribute it for both personal and commercial purposes.

---
## 🙌 Credits
- [shadcn/ui](https://ui.shadcn.com/) – Elegant UI component library  
- [Tailwind CSS](https://tailwindcss.com/) – Utility-first styling  
- [Lovable](https://lovable.dev/) – For assisting in project scaffolding
